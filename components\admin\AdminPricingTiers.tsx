"use client";

import { adminApi } from "@/lib/api-client";
import { PricingTier, PricingTierInsert } from "@/lib/types";
import { AlertCircle, DollarSign, Edit, Loader2, Plus, Save, Trash2, X } from "lucide-react";
import { useEffect, useState } from "react";
import Button from "./ui/Button";

interface PricingTierWithService extends PricingTier {
	service?: {
		id: string;
		name: string;
		category?: string;
		is_active: boolean;
	};
}

interface Service {
	id: string;
	name: string;
	category?: string;
	is_active: boolean;
}

const AdminPricingTiers = () => {
	const [pricingTiers, setPricingTiers] = useState<PricingTierWithService[]>([]);
	const [services, setServices] = useState<Service[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [isCreating, setIsCreating] = useState(false);
	const [editingId, setEditingId] = useState<string | null>(null);
	const [editForm, setEditForm] = useState<Partial<PricingTierInsert>>({});
	const [saving, setSaving] = useState(false);

	useEffect(() => {
		fetchData();
	}, []);

	const fetchData = async () => {
		try {
			setLoading(true);
			setError(null);
			
			// Fetch pricing tiers and services in parallel
			const [pricingTiersResponse, servicesResponse] = await Promise.all([
				adminApi.getPricingTiers(),
				adminApi.getServices()
			]);

			if (pricingTiersResponse?.pricingTiers) {
				setPricingTiers(pricingTiersResponse.pricingTiers);
			}
			if (servicesResponse?.services) {
				setServices(servicesResponse.services);
			}
		} catch (err) {
			console.error("Error fetching data:", err);
			setError("Erreur lors du chargement des données");
		} finally {
			setLoading(false);
		}
	};

	const handleCreate = () => {
		setIsCreating(true);
		setEditForm({
			service_id: "",
			tier_name: "",
			min_age: null,
			max_age: null,
			price: 0,
			is_active: true,
		});
	};

	const handleEdit = (pricingTier: PricingTierWithService) => {
		setEditingId(pricingTier.id);
		setEditForm({
			service_id: pricingTier.service_id,
			tier_name: pricingTier.tier_name,
			min_age: pricingTier.min_age,
			max_age: pricingTier.max_age,
			price: pricingTier.price,
			is_active: pricingTier.is_active,
		});
	};

	const handleSave = async () => {
		if (!editForm.service_id || !editForm.tier_name || editForm.price === undefined) {
			setError("Veuillez remplir tous les champs obligatoires");
			return;
		}

		if (editForm.min_age !== null && editForm.max_age !== null && editForm.min_age > editForm.max_age) {
			setError("L'âge minimum ne peut pas être supérieur à l'âge maximum");
			return;
		}

		try {
			setSaving(true);
			setError(null);

			if (isCreating) {
				await adminApi.createPricingTier(editForm);
				setIsCreating(false);
			} else if (editingId) {
				await adminApi.updatePricingTier(editingId, editForm);
				setEditingId(null);
			}

			setEditForm({});
			await fetchData(); // Refresh the list
		} catch (err) {
			console.error("Error saving pricing tier:", err);
			setError("Erreur lors de la sauvegarde du tarif");
		} finally {
			setSaving(false);
		}
	};

	const handleCancel = () => {
		setIsCreating(false);
		setEditingId(null);
		setEditForm({});
		setError(null);
	};

	const handleDelete = async (pricingTierId: string) => {
		if (window.confirm("Êtes-vous sûr de vouloir supprimer ce tarif ?")) {
			try {
				await adminApi.deletePricingTier(pricingTierId);
				await fetchData(); // Refresh the list
			} catch (err) {
				console.error("Error deleting pricing tier:", err);
				setError("Erreur lors de la suppression du tarif");
			}
		}
	};

	const handleInputChange = (field: keyof PricingTierInsert, value: any) => {
		setEditForm((prev: Partial<PricingTierInsert>) => ({ ...prev, [field]: value }));
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat('fr-FR', {
			style: 'currency',
			currency: 'EUR'
		}).format(amount);
	};

	const getAgeRangeText = (minAge: number | null, maxAge: number | null) => {
		if (minAge === null && maxAge === null) return "Tous âges";
		if (minAge === null) return `Jusqu'à ${maxAge} ans`;
		if (maxAge === null) return `${minAge} ans et plus`;
		return `${minAge} - ${maxAge} ans`;
	};

	if (loading) {
		return (
			<div className="p-6">
				<div className="flex items-center justify-center h-64">
					<Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
				</div>
			</div>
		);
	}

	return (
		<div className="p-6">
			<div className="flex justify-between items-center mb-8">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Tarifs</h1>
					<p className="text-gray-600">Gérez les tarifs par tranche d'âge pour vos services</p>
				</div>
				<Button onClick={handleCreate} icon={Plus}>
					Nouveau Tarif
				</Button>
			</div>

			{/* Error Message */}
			{error && (
				<div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
					<div className="flex items-center">
						<AlertCircle className="h-5 w-5 text-red-600 mr-2" />
						<span className="text-red-700">{error}</span>
					</div>
				</div>
			)}

			{/* Create/Edit Form */}
			{(isCreating || editingId) && (
				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
					<h2 className="text-xl font-bold text-gray-900 mb-6">
						{isCreating ? "Créer un tarif" : "Modifier le tarif"}
					</h2>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Service *
							</label>
							<select
								value={editForm.service_id || ""}
								onChange={(e) => handleInputChange("service_id", e.target.value)}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							>
								<option value="">Sélectionner un service</option>
								{services.filter(s => s.is_active).map((service) => (
									<option key={service.id} value={service.id}>
										{service.name} {service.category && `(${service.category})`}
									</option>
								))}
							</select>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Nom du tarif *
							</label>
							<input
								type="text"
								value={editForm.tier_name || ""}
								onChange={(e) => handleInputChange("tier_name", e.target.value)}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Ex: Enfant, Adulte, Senior..."
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Âge minimum
							</label>
							<input
								type="number"
								min="0"
								max="120"
								value={editForm.min_age || ""}
								onChange={(e) => handleInputChange("min_age", e.target.value ? parseInt(e.target.value) : null)}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Laisser vide pour aucune limite"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Âge maximum
							</label>
							<input
								type="number"
								min="0"
								max="120"
								value={editForm.max_age || ""}
								onChange={(e) => handleInputChange("max_age", e.target.value ? parseInt(e.target.value) : null)}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Laisser vide pour aucune limite"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Prix (€) *
							</label>
							<input
								type="number"
								step="0.01"
								min="0"
								value={editForm.price || 0}
								onChange={(e) => handleInputChange("price", parseFloat(e.target.value))}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Statut
							</label>
							<select
								value={editForm.is_active ? "true" : "false"}
								onChange={(e) => handleInputChange("is_active", e.target.value === "true")}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							>
								<option value="true">Actif</option>
								<option value="false">Inactif</option>
							</select>
						</div>
					</div>

					<div className="flex justify-end gap-4 mt-6 pt-6 border-t">
						<Button variant="outline" onClick={handleCancel} icon={X} disabled={saving}>
							Annuler
						</Button>
						<Button onClick={handleSave} icon={saving ? Loader2 : Save} disabled={saving}>
							{saving ? "Sauvegarde..." : isCreating ? "Créer le tarif" : "Sauvegarder"}
						</Button>
					</div>
				</div>
			)}

			{/* Pricing Tiers List */}
			{!loading && (
				<div className="bg-white rounded-xl shadow-sm border border-gray-200">
					<div className="p-6 border-b border-gray-200">
						<h2 className="text-xl font-bold text-gray-900">Tarifs ({pricingTiers.length})</h2>
					</div>
					<div className="divide-y divide-gray-200">
						{pricingTiers.length === 0 ? (
							<div className="p-12 text-center">
								<DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
								<p className="text-gray-500">Aucun tarif trouvé</p>
							</div>
						) : (
							pricingTiers.map((tier) => (
								<div key={tier.id} className="p-6">
									<div className="flex items-start justify-between">
										<div className="flex-1">
											<div className="flex items-center gap-3 mb-2">
												<DollarSign className="h-5 w-5 text-emerald-600" />
												<h3 className="text-lg font-semibold text-gray-900">{tier.tier_name}</h3>
												<span
													className={`px-2 py-1 text-xs font-medium rounded-full ${
														tier.is_active
															? "bg-green-100 text-green-800"
															: "bg-red-100 text-red-800"
													}`}
												>
													{tier.is_active ? "Actif" : "Inactif"}
												</span>
											</div>
											<div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
												<div>
													<span className="text-gray-500">Service:</span>
													<p className="font-medium">{tier.service?.name || "Service supprimé"}</p>
												</div>
												<div>
													<span className="text-gray-500">Tranche d'âge:</span>
													<p className="font-medium">{getAgeRangeText(tier.min_age, tier.max_age)}</p>
												</div>
												<div>
													<span className="text-gray-500">Prix:</span>
													<p className="font-medium text-emerald-600 text-lg">
														{formatCurrency(tier.price)}
													</p>
												</div>
											</div>
										</div>
										<div className="flex gap-2 ml-4">
											<Button
												variant="outline"
												size="sm"
												icon={Edit}
												onClick={() => handleEdit(tier)}
												disabled={saving}
											>
												Modifier
											</Button>
											<Button
												variant="outline"
												size="sm"
												icon={Trash2}
												onClick={() => handleDelete(tier.id)}
												className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
												disabled={saving}
											>
												Supprimer
											</Button>
										</div>
									</div>
								</div>
							))
						)}
					</div>
				</div>
			)}
		</div>
	);
};

export default AdminPricingTiers;
