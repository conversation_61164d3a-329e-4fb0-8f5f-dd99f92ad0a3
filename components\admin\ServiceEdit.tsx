"use client";

import { adminApi } from "@/lib/api-client";
import { Service, ServiceInsert } from "@/lib/types";
import { AlertCircle, ArrowLeft, Loader2, Save } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import Button from "./ui/Button";

interface ServiceEditProps {
	serviceId: string;
}

const ServiceEdit = ({ serviceId }: ServiceEditProps) => {
	const router = useRouter();
	const [service, setService] = useState<Service | null>(null);
	const [loading, setLoading] = useState(true);
	const [saving, setSaving] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [editForm, setEditForm] = useState<Partial<ServiceInsert>>({});

	useEffect(() => {
		fetchService();
	}, [serviceId]);

	const fetchService = async () => {
		try {
			setLoading(true);
			setError(null);
			const response = await adminApi.getService(serviceId);
			if (response?.service) {
				const serviceData = response.service;
				setService(serviceData);
				setEditForm({
					name: serviceData.name,
					description: serviceData.description || "",
					duration_minutes: serviceData.duration_minutes,
					buffer_time_minutes: serviceData.buffer_time_minutes || 0,
					base_price: serviceData.base_price,
					max_participants: serviceData.max_participants,
					min_age: serviceData.min_age || undefined,
					max_age: serviceData.max_age || undefined,
					is_family_friendly: serviceData.is_family_friendly || false,
					category: serviceData.category || "",
					location: serviceData.location || "",
					image_url: serviceData.image_url || "",
					features: serviceData.features || [],
					is_active: serviceData.is_active,
					requires_employee: serviceData.requires_employee,
					requires_qualification: serviceData.requires_qualification || false,
					auto_assign_employees: serviceData.auto_assign_employees || false,
				});
			}
		} catch (err) {
			console.error("Error fetching service:", err);
			setError("Erreur lors du chargement du service");
		} finally {
			setLoading(false);
		}
	};

	const handleInputChange = (field: keyof ServiceInsert, value: any) => {
		setEditForm((prev: Partial<ServiceInsert>) => ({ ...prev, [field]: value }));
	};

	const handleSave = async () => {
		if (!editForm.name || !editForm.duration_minutes || !editForm.base_price || !editForm.max_participants) {
			setError("Veuillez remplir tous les champs obligatoires");
			return;
		}

		try {
			setSaving(true);
			setError(null);

			await adminApi.updateService(serviceId, editForm);
			
			// Refresh service data
			await fetchService();
			
			// Show success message or redirect
			router.push("/admin/services");
		} catch (err) {
			console.error("Error saving service:", err);
			setError("Erreur lors de la sauvegarde du service");
		} finally {
			setSaving(false);
		}
	};

	const handleCancel = () => {
		router.push("/admin/services");
	};

	if (loading) {
		return (
			<div className="p-6">
				<div className="flex items-center justify-center h-64">
					<Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
				</div>
			</div>
		);
	}

	if (!service) {
		return (
			<div className="p-6">
				<div className="text-center">
					<h1 className="text-2xl font-bold text-gray-900 mb-4">Service non trouvé</h1>
					<Button onClick={() => router.push("/admin/services")} icon={ArrowLeft}>
						Retour aux services
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="p-6">
			{/* Header */}
			<div className="flex items-center gap-4 mb-8">
				<Button
					variant="outline"
					onClick={handleCancel}
					icon={ArrowLeft}
				>
					Retour
				</Button>
				<div>
					<h1 className="text-3xl font-bold text-gray-900">Modifier le service</h1>
					<p className="text-gray-600">Modifiez les informations de base du service</p>
				</div>
			</div>

			{/* Error Message */}
			{error && (
				<div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
					<div className="flex items-center">
						<AlertCircle className="h-5 w-5 text-red-600 mr-2" />
						<span className="text-red-700">{error}</span>
					</div>
				</div>
			)}

			{/* Edit Form */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
				<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
					{/* Basic Information */}
					<div className="space-y-4">
						<h3 className="text-lg font-semibold text-gray-900 mb-4">Informations de base</h3>
						
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Nom du service *
							</label>
							<input
								type="text"
								value={editForm.name || ""}
								onChange={(e) => handleInputChange("name", e.target.value)}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Nom du service"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Catégorie
							</label>
							<input
								type="text"
								value={editForm.category || ""}
								onChange={(e) => handleInputChange("category", e.target.value)}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Ex: Plongée, Kayak, Randonnée..."
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Lieu
							</label>
							<input
								type="text"
								value={editForm.location || ""}
								onChange={(e) => handleInputChange("location", e.target.value)}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Lieu de l'activité"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Description
							</label>
							<textarea
								value={editForm.description || ""}
								onChange={(e) => handleInputChange("description", e.target.value)}
								rows={4}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Description détaillée du service"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								URL de l'image
							</label>
							<input
								type="url"
								value={editForm.image_url || ""}
								onChange={(e) => handleInputChange("image_url", e.target.value)}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="https://example.com/image.jpg"
							/>
						</div>
					</div>

					{/* Pricing and Capacity */}
					<div className="space-y-4">
						<h3 className="text-lg font-semibold text-gray-900 mb-4">Tarifs et capacité</h3>
						
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Prix de base (€) *
							</label>
							<input
								type="number"
								step="0.01"
								min="0"
								value={editForm.base_price || 0}
								onChange={(e) => handleInputChange("base_price", parseFloat(e.target.value))}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Durée (minutes) *
							</label>
							<input
								type="number"
								min="1"
								value={editForm.duration_minutes || 120}
								onChange={(e) => handleInputChange("duration_minutes", parseInt(e.target.value))}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Temps de préparation (minutes)
							</label>
							<input
								type="number"
								min="0"
								value={editForm.buffer_time_minutes || 0}
								onChange={(e) => handleInputChange("buffer_time_minutes", parseInt(e.target.value))}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Participants maximum *
							</label>
							<input
								type="number"
								min="1"
								value={editForm.max_participants || 10}
								onChange={(e) => handleInputChange("max_participants", parseInt(e.target.value))}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							/>
						</div>

						<div className="grid grid-cols-2 gap-4">
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Âge minimum
								</label>
								<input
									type="number"
									min="0"
									max="120"
									value={editForm.min_age || ""}
									onChange={(e) => handleInputChange("min_age", e.target.value ? parseInt(e.target.value) : null)}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									placeholder="Aucune limite"
								/>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Âge maximum
								</label>
								<input
									type="number"
									min="0"
									max="120"
									value={editForm.max_age || ""}
									onChange={(e) => handleInputChange("max_age", e.target.value ? parseInt(e.target.value) : null)}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									placeholder="Aucune limite"
								/>
							</div>
						</div>

						{/* Options */}
						<div className="space-y-3">
							<h4 className="text-md font-medium text-gray-900">Options</h4>
							
							<label className="flex items-center gap-3">
								<input
									type="checkbox"
									checked={editForm.is_active || false}
									onChange={(e) => handleInputChange("is_active", e.target.checked)}
									className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
								/>
								<span className="text-sm text-gray-700">Service actif</span>
							</label>

							<label className="flex items-center gap-3">
								<input
									type="checkbox"
									checked={editForm.requires_employee || false}
									onChange={(e) => handleInputChange("requires_employee", e.target.checked)}
									className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
								/>
								<span className="text-sm text-gray-700">Nécessite un employé</span>
							</label>

							<label className="flex items-center gap-3">
								<input
									type="checkbox"
									checked={editForm.requires_qualification || false}
									onChange={(e) => handleInputChange("requires_qualification", e.target.checked)}
									className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
								/>
								<span className="text-sm text-gray-700">Nécessite une qualification spécifique</span>
							</label>

							<label className="flex items-center gap-3">
								<input
									type="checkbox"
									checked={editForm.auto_assign_employees || false}
									onChange={(e) => handleInputChange("auto_assign_employees", e.target.checked)}
									className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
								/>
								<span className="text-sm text-gray-700">Attribution automatique des employés</span>
							</label>

							<label className="flex items-center gap-3">
								<input
									type="checkbox"
									checked={editForm.is_family_friendly || false}
									onChange={(e) => handleInputChange("is_family_friendly", e.target.checked)}
									className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
								/>
								<span className="text-sm text-gray-700">Adapté aux familles</span>
							</label>
						</div>
					</div>
				</div>

				{/* Action Buttons */}
				<div className="flex justify-end gap-4 mt-8 pt-6 border-t">
					<Button variant="outline" onClick={handleCancel} disabled={saving}>
						Annuler
					</Button>
					<Button onClick={handleSave} icon={saving ? Loader2 : Save} disabled={saving}>
						{saving ? "Sauvegarde..." : "Sauvegarder les modifications"}
					</Button>
				</div>
			</div>
		</div>
	);
};

export default ServiceEdit;
