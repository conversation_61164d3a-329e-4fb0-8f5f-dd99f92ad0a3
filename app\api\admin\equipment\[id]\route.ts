import { logAdminAction, withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { Database } from "@/lib/types";
import { NextRequest, NextResponse } from "next/server";

type Equipment = Database["public"]["Tables"]["equipment"]["Row"];
type EquipmentUpdate = Database["public"]["Tables"]["equipment"]["Update"];

// GET /api/admin/equipment/[id] - Get single equipment with full details
export const GET = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const equipmentId = params.id;

		const { data: equipment, error } = await supabaseAdmin
			.from("equipment")
			.select(
				`
        *,
        service_equipment_requirements (
          id,
          capacity_per_participant,
          service:services (
            id,
            name,
            category,
            is_active,
            max_participants
          )
        ),
        equipment_reservations (
          id,
          quantity_reserved,
          status,
          created_at,
          start_time,
          end_time,
          reservation:reservations (
            id,
            reservation_number,
            status,
            service:services (name)
          )
        ),
        equipment_utilization_history (
          id,
          usage_date,
          total_capacity_hours,
          utilized_capacity_hours,
          utilization_rate,
          maintenance_hours,
          downtime_hours,
          revenue_generated,
          bookings_count
        )
      `
			)
			.eq("id", equipmentId)
			.single();

		if (error) {
			if (error.code === "PGRST116") {
				return NextResponse.json({ error: "Equipment not found" }, { status: 404 });
			}
			console.error("Error fetching equipment:", error);
			return NextResponse.json({ error: "Failed to fetch equipment" }, { status: 500 });
		}

		// Calculate utilization statistics
		const reservations = equipment.equipment_reservations || [];
		const utilizationHistory = equipment.equipment_utilization_history || [];
		const serviceRequirements = equipment.service_equipment_requirements || [];

		// Current reservations (active/upcoming)
		const activeReservations = reservations.filter(
			(r) => r.status === "reserved" && r.start_time && new Date(r.start_time) > new Date()
		);

		// Calculate utilization for last 30 days
		const thirtyDaysAgo = new Date();
		thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

		const recentUtilization = utilizationHistory.filter((u) => new Date(u.usage_date) >= thirtyDaysAgo);

		const avgUtilizationRate =
			recentUtilization.length > 0
				? recentUtilization.reduce((sum, u) => sum + (u.utilization_rate || 0), 0) / recentUtilization.length
				: 0;

		const totalRevenue = recentUtilization.reduce((sum, u) => sum + (u.revenue_generated || 0), 0);
		const totalMaintenanceHours = recentUtilization.reduce((sum, u) => sum + (u.maintenance_hours || 0), 0);
		const totalDowntimeHours = recentUtilization.reduce((sum, u) => sum + (u.downtime_hours || 0), 0);

		// Associated services analysis
		const activeServices = serviceRequirements.filter((req) => req.service?.is_active);
		const serviceUtilization = serviceRequirements.map((req) => {
			const serviceReservations = reservations.filter(
				(r) => r.time_slot?.reservation?.service?.name === req.service?.name
			);
			return {
				serviceName: req.service?.name,
				category: req.service?.category,
				capacityPerParticipant: req.capacity_per_participant,
				maxParticipants: req.service?.max_participants,
				recentBookings: serviceReservations.length,
				isActive: req.service?.is_active,
			};
		});

		// Upcoming maintenance/downtime
		const upcomingMaintenance = []; // Would come from a maintenance schedule table

		// Capacity analysis
		const peakUsageTimes = []; // Would need to analyze reservation patterns

		return NextResponse.json({
			equipment: {
				...equipment,
				stats: {
					totalCapacity: equipment.total_capacity,
					currentlyReserved: activeReservations.reduce((sum, r) => sum + r.reserved_capacity, 0),
					availableCapacity:
						equipment.total_capacity - activeReservations.reduce((sum, r) => sum + r.reserved_capacity, 0),
					utilizationRate: Math.round(avgUtilizationRate * 100) / 100,
					totalReservations: reservations.length,
					activeReservations: activeReservations.length,
					associatedServices: activeServices.length,
					revenueGenerated: totalRevenue,
					maintenanceHours: totalMaintenanceHours,
					downtimeHours: totalDowntimeHours,
				},
				utilization: {
					last30Days: recentUtilization.map((u) => ({
						date: u.usage_date,
						utilizationRate: u.utilization_rate,
						revenueGenerated: u.revenue_generated,
						bookingsCount: u.bookings_count,
						maintenanceHours: u.maintenance_hours,
						downtimeHours: u.downtime_hours,
					})),
					averageUtilization: avgUtilizationRate,
					peakUsageTimes,
					serviceBreakdown: serviceUtilization,
				},
				schedule: {
					activeReservations: activeReservations.map((r) => ({
						id: r.id,
						reservedCapacity: r.quantity_reserved,
						startTime: r.start_time,
						endTime: r.end_time,
						reservationNumber: r.reservation?.reservation_number,
						serviceName: r.reservation?.service?.name,
						status: r.reservation?.status,
					})),
					upcomingMaintenance,
				},
			},
		});
	} catch (error) {
		console.error("Equipment GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "equipment:read");

// PUT /api/admin/equipment/[id] - Update single equipment
export const PUT = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const equipmentId = params.id;
		const updates: EquipmentUpdate = await request.json();

		// Get current equipment for audit log
		const { data: currentEquipment } = await supabaseAdmin
			.from("equipment")
			.select("*")
			.eq("id", equipmentId)
			.single();

		if (!currentEquipment) {
			return NextResponse.json({ error: "Equipment not found" }, { status: 404 });
		}

		// Validate capacity changes
		if (updates.total_capacity && updates.total_capacity < currentEquipment.total_capacity) {
			// Check if reducing capacity would affect existing reservations
			const { data: futureReservations } = await supabaseAdmin
				.from("equipment_reservations")
				.select(
					`
          quantity_reserved,
          start_time,
          reservation:reservations (status)
        `
				)
				.eq("equipment_id", equipmentId)
				.eq("status", "reserved");

			const activeReservations =
				futureReservations?.filter(
					(r) => r.start_time && new Date(r.start_time) > new Date() && r.reservation?.status !== "cancelled"
				) || [];

			const maxReservedCapacity = Math.max(...activeReservations.map((r) => r.quantity_reserved), 0);

			if (updates.total_capacity < maxReservedCapacity) {
				return NextResponse.json(
					{
						error: "Cannot reduce capacity below current reservations",
						currentMaxReservation: maxReservedCapacity,
						proposedCapacity: updates.total_capacity,
					},
					{ status: 400 }
				);
			}
		}

		// Update equipment
		const { data: updatedEquipment, error } = await supabaseAdmin
			.from("equipment")
			.update(updates)
			.eq("id", equipmentId)
			.select()
			.single();

		if (error) {
			console.error("Error updating equipment:", error);
			return NextResponse.json({ error: "Failed to update equipment" }, { status: 500 });
		}

		// Log admin action
		await logAdminAction(user.id, "UPDATE", "equipment", equipmentId, currentEquipment, updatedEquipment, request);

		return NextResponse.json({ equipment: updatedEquipment });
	} catch (error) {
		console.error("Equipment PUT error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "equipment:write");

// DELETE /api/admin/equipment/[id] - Completely delete equipment and cleanup references
export const DELETE = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const equipmentId = params.id;

		// Get equipment for audit log
		const { data: equipmentToDelete } = await supabaseAdmin
			.from("equipment")
			.select("*")
			.eq("id", equipmentId)
			.single();

		if (!equipmentToDelete) {
			return NextResponse.json({ error: "Equipment not found" }, { status: 404 });
		}

		// Check for future reservations
		const { data: futureReservations } = await supabaseAdmin
			.from("equipment_reservations")
			.select("id, start_time")
			.eq("equipment_id", equipmentId)
			.gte("start_time", new Date().toISOString());

		if (futureReservations && futureReservations.length > 0) {
			return NextResponse.json(
				{
					error: "Cannot delete equipment with future reservations",
					futureReservations: futureReservations.length,
				},
				{ status: 400 }
			);
		}

		// Start cascade deletion - delete related records first

		// 1. Delete service equipment requirements
		await supabaseAdmin.from("service_equipment_requirements").delete().eq("equipment_id", equipmentId);

		// 2. Handle completed/cancelled equipment reservations - keep for historical data but mark equipment as deleted
		const { data: historicalReservations } = await supabaseAdmin
			.from("equipment_reservations")
			.select("id")
			.eq("equipment_id", equipmentId)
			.in("status", ["completed", "cancelled"]);

		if (historicalReservations && historicalReservations.length > 0) {
			// Add a note to historical reservations that the equipment was deleted
			await supabaseAdmin
				.from("equipment_reservations")
				.update({
					notes: `Equipment "${equipmentToDelete.name}" was deleted on ${new Date().toISOString()}. ${
						equipmentToDelete.notes || ""
					}`.trim(),
				})
				.eq("equipment_id", equipmentId)
				.in("status", ["completed", "cancelled"]);
		}

		// 3. Finally, delete the equipment itself
		const { error: deleteError } = await supabaseAdmin.from("equipment").delete().eq("id", equipmentId);

		if (deleteError) {
			console.error("Error deleting equipment:", deleteError);
			return NextResponse.json({ error: "Failed to delete equipment" }, { status: 500 });
		}

		// Log admin action
		await logAdminAction(user.id, "DELETE", "equipment", equipmentId, equipmentToDelete, null, request);

		return NextResponse.json({
			message: "Equipment and all related data deleted successfully",
			deletedEquipment: equipmentToDelete,
			historicalReservations: historicalReservations?.length || 0,
		});
	} catch (error) {
		console.error("Equipment DELETE error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "equipment:write");
