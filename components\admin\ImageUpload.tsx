"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { AlertCircle, Loader2, Upload, X } from "lucide-react";
import { useRef, useState } from "react";

interface ImageUploadProps {
	value?: string;
	onChange: (url: string) => void;
	onRemove?: () => void;
	disabled?: boolean;
	className?: string;
}

const ImageUpload = ({ value, onChange, onRemove, disabled, className }: ImageUploadProps) => {
	const [uploading, setUploading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const fileInputRef = useRef<HTMLInputElement>(null);

	const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (!file) return;

		// Validate file type
		if (!file.type.startsWith("image/")) {
			setError("Veuillez sélectionner un fichier image valide");
			return;
		}

		// Validate file size (max 5MB)
		if (file.size > 5 * 1024 * 1024) {
			setError("La taille du fichier ne doit pas dépasser 5MB");
			return;
		}

		try {
			setUploading(true);
			setError(null);

			// Create FormData for upload
			const formData = new FormData();
			formData.append("file", file);

			// Upload to our API endpoint
			const response = await fetch("/api/upload/image", {
				method: "POST",
				body: formData,
			});

			if (!response.ok) {
				throw new Error("Erreur lors de l'upload de l'image");
			}

			const data = await response.json();
			
			if (data.url) {
				onChange(data.url);
			} else {
				throw new Error("URL de l'image non reçue");
			}
		} catch (err) {
			console.error("Upload error:", err);
			setError(err instanceof Error ? err.message : "Erreur lors de l'upload");
		} finally {
			setUploading(false);
			// Reset file input
			if (fileInputRef.current) {
				fileInputRef.current.value = "";
			}
		}
	};

	const handleUploadClick = () => {
		fileInputRef.current?.click();
	};

	const handleRemove = () => {
		if (onRemove) {
			onRemove();
		} else {
			onChange("");
		}
	};

	return (
		<div className={`space-y-4 ${className}`}>
			{/* Hidden file input */}
			<input
				ref={fileInputRef}
				type="file"
				accept="image/*"
				onChange={handleFileSelect}
				className="hidden"
				disabled={disabled || uploading}
			/>

			{/* Current image preview */}
			{value && (
				<div className="relative">
					<img
						src={value}
						alt="Preview"
						className="w-full h-48 object-cover rounded-lg border border-gray-300"
					/>
					<Button
						type="button"
						variant="outline"
						size="sm"
						onClick={handleRemove}
						disabled={disabled || uploading}
						className="absolute top-2 right-2 bg-white/90 hover:bg-white"
					>
						<X className="w-4 h-4" />
					</Button>
				</div>
			)}

			{/* Upload button */}
			<div className="flex flex-col gap-2">
				<Button
					type="button"
					variant="outline"
					onClick={handleUploadClick}
					disabled={disabled || uploading}
					className="w-full"
				>
					{uploading ? (
						<>
							<Loader2 className="w-4 h-4 mr-2 animate-spin" />
							Upload en cours...
						</>
					) : (
						<>
							<Upload className="w-4 h-4 mr-2" />
							{value ? "Changer l'image" : "Télécharger une image"}
						</>
					)}
				</Button>

				{/* Error message */}
				{error && (
					<div className="flex items-center gap-2 text-red-600 text-sm">
						<AlertCircle className="w-4 h-4" />
						{error}
					</div>
				)}

				{/* Help text */}
				<p className="text-xs text-gray-500">
					Formats acceptés: JPG, PNG, GIF. Taille max: 5MB
				</p>
			</div>
		</div>
	);
};

export default ImageUpload;
