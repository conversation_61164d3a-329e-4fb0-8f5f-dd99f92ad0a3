import { withAdminAuth } from '@/lib/admin-auth'
import { supabaseAdmin } from '@/lib/supabase'
import { NextRequest, NextResponse } from 'next/server'

// GET /api/admin/services/[id]/scheduling-rules - Get scheduling rules for a service
export const GET = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
  try {
    const serviceId = params.id

    const { data: rules, error } = await supabaseAdmin
      .from('service_scheduling_rules')
      .select('*')
      .eq('service_id', serviceId)
      .order('day_of_week', { ascending: true })

    if (error) {
      console.error('Error fetching scheduling rules:', error)
      return NextResponse.json({ error: 'Failed to fetch scheduling rules' }, { status: 500 })
    }

    return NextResponse.json({ rules })
  } catch (error) {
    console.error('Scheduling rules GET error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'services:read')

// POST /api/admin/services/[id]/scheduling-rules - Create new scheduling rule
export const POST = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
  try {
    const serviceId = params.id
    const ruleData = await request.json()

    // Validate required fields
    if (!ruleData.operating_start_time || !ruleData.operating_end_time) {
      return NextResponse.json(
        { error: 'Operating start and end times are required' },
        { status: 400 }
      )
    }

    // Ensure service_id matches the URL parameter
    ruleData.service_id = serviceId

    const { data: rule, error } = await supabaseAdmin
      .from('service_scheduling_rules')
      .insert(ruleData)
      .select()
      .single()

    if (error) {
      console.error('Error creating scheduling rule:', error)
      return NextResponse.json({ error: 'Failed to create scheduling rule' }, { status: 500 })
    }

    return NextResponse.json({ rule }, { status: 201 })
  } catch (error) {
    console.error('Scheduling rules POST error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'services:write')
