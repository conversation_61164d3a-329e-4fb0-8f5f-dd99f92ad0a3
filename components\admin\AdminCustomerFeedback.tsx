"use client";

import { adminApi } from "@/lib/api-client";
import { CustomerFeedback } from "@/lib/types";
import { AlertCircle, Eye, EyeOff, Loader2, MessageSquare, Reply, Save, Star, X } from "lucide-react";
import { useEffect, useState } from "react";
import Button from "./ui/Button";

interface CustomerFeedbackWithRelations extends CustomerFeedback {
	customer?: {
		id: string;
		first_name: string;
		last_name: string;
		email: string;
	};
	reservation?: {
		id: string;
		reservation_number: string;
		start_time: string;
		service?: {
			id: string;
			name: string;
			category?: string;
		};
	};
	responder?: {
		id: string;
		first_name: string;
		last_name: string;
		email: string;
	};
}

const AdminCustomerFeedback = () => {
	const [feedback, setFeedback] = useState<CustomerFeedbackWithRelations[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [respondingTo, setRespondingTo] = useState<string | null>(null);
	const [responseText, setResponseText] = useState("");
	const [responsePublic, setResponsePublic] = useState(true);
	const [saving, setSaving] = useState(false);
	const [filters, setFilters] = useState({
		rating: "",
		is_public: "",
		has_response: "",
	});

	useEffect(() => {
		fetchFeedback();
	}, [filters]);

	const fetchFeedback = async () => {
		try {
			setLoading(true);
			setError(null);

			const params: Record<string, string> = {};
			if (filters.rating) params.rating = filters.rating;
			if (filters.is_public) params.is_public = filters.is_public;
			if (filters.has_response) params.has_response = filters.has_response;

			const response = await adminApi.getCustomerFeedback(params);
			if (response?.feedback) {
				setFeedback(response.feedback);
			}
		} catch (err) {
			console.error("Error fetching customer feedback:", err);
			setError("Erreur lors du chargement des avis clients");
		} finally {
			setLoading(false);
		}
	};

	const handleRespond = (feedbackId: string, currentResponse?: string) => {
		setRespondingTo(feedbackId);
		setResponseText(currentResponse || "");
		setResponsePublic(true);
	};

	const handleSaveResponse = async () => {
		if (!respondingTo || !responseText.trim()) {
			setError("Veuillez saisir une réponse");
			return;
		}

		try {
			setSaving(true);
			setError(null);

			await adminApi.respondToFeedback(respondingTo, {
				response_text: responseText.trim(),
				is_public: responsePublic,
			});

			setRespondingTo(null);
			setResponseText("");
			await fetchFeedback(); // Refresh the list
		} catch (err) {
			console.error("Error saving response:", err);
			setError("Erreur lors de la sauvegarde de la réponse");
		} finally {
			setSaving(false);
		}
	};

	const handleCancelResponse = () => {
		setRespondingTo(null);
		setResponseText("");
		setError(null);
	};

	const handleToggleVisibility = async (feedbackId: string, currentVisibility: boolean | null) => {
		try {
			await adminApi.updateFeedback(feedbackId, {
				is_public: !currentVisibility,
			});
			await fetchFeedback(); // Refresh the list
		} catch (err) {
			console.error("Error updating visibility:", err);
			setError("Erreur lors de la mise à jour de la visibilité");
		}
	};

	const renderStars = (rating: number, maxRating: number = 5) => {
		return Array.from({ length: maxRating }, (_, i) => (
			<Star key={i} className={`h-4 w-4 ${i < rating ? "text-yellow-400 fill-current" : "text-gray-300"}`} />
		));
	};

	const formatDate = (dateString: string | null) => {
		if (!dateString) return "N/A";
		return new Date(dateString).toLocaleDateString("fr-FR", {
			year: "numeric",
			month: "short",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit",
		});
	};

	const getCustomerName = (customer?: CustomerFeedbackWithRelations["customer"]) => {
		if (!customer) return "Client inconnu";
		return `${customer.first_name} ${customer.last_name}`;
	};

	if (loading) {
		return (
			<div className="p-6">
				<div className="flex items-center justify-center h-64">
					<Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
				</div>
			</div>
		);
	}

	return (
		<div className="p-6">
			<div className="flex justify-between items-center mb-8">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Avis Clients</h1>
					<p className="text-gray-600">Gérez les avis et réponses aux commentaires clients</p>
				</div>
			</div>

			{/* Filters */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
				<h2 className="text-lg font-semibold text-gray-900 mb-4">Filtres</h2>
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-2">Note</label>
						<select
							value={filters.rating}
							onChange={(e) => setFilters((prev) => ({ ...prev, rating: e.target.value }))}
							className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
						>
							<option value="">Toutes les notes</option>
							<option value="5">5 étoiles</option>
							<option value="4">4 étoiles</option>
							<option value="3">3 étoiles</option>
							<option value="2">2 étoiles</option>
							<option value="1">1 étoile</option>
						</select>
					</div>

					<div>
						<label className="block text-sm font-medium text-gray-700 mb-2">Visibilité</label>
						<select
							value={filters.is_public}
							onChange={(e) => setFilters((prev) => ({ ...prev, is_public: e.target.value }))}
							className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
						>
							<option value="">Tous</option>
							<option value="true">Public</option>
							<option value="false">Privé</option>
						</select>
					</div>

					<div>
						<label className="block text-sm font-medium text-gray-700 mb-2">Réponse</label>
						<select
							value={filters.has_response}
							onChange={(e) => setFilters((prev) => ({ ...prev, has_response: e.target.value }))}
							className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
						>
							<option value="">Tous</option>
							<option value="true">Avec réponse</option>
							<option value="false">Sans réponse</option>
						</select>
					</div>
				</div>
			</div>

			{/* Error Message */}
			{error && (
				<div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
					<div className="flex items-center">
						<AlertCircle className="h-5 w-5 text-red-600 mr-2" />
						<span className="text-red-700">{error}</span>
					</div>
				</div>
			)}

			{/* Feedback List */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200">
				<div className="p-6 border-b border-gray-200">
					<h2 className="text-xl font-bold text-gray-900">Avis clients ({feedback.length})</h2>
				</div>
				<div className="divide-y divide-gray-200">
					{feedback.length === 0 ? (
						<div className="p-12 text-center">
							<MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
							<p className="text-gray-500">Aucun avis trouvé</p>
						</div>
					) : (
						feedback.map((item) => (
							<div key={item.id} className="p-6">
								<div className="flex items-start justify-between mb-4">
									<div className="flex-1">
										<div className="flex items-center gap-3 mb-2">
											<div className="flex items-center gap-1">{renderStars(item.rating)}</div>
											<span className="text-sm text-gray-500">
												par {getCustomerName(item.customer)}
											</span>
											<span className="text-sm text-gray-400">
												• {formatDate(item.created_at)}
											</span>
											<span
												className={`px-2 py-1 text-xs font-medium rounded-full ${
													item.is_public
														? "bg-green-100 text-green-800"
														: "bg-gray-100 text-gray-800"
												}`}
											>
												{item.is_public ? "Public" : "Privé"}
											</span>
										</div>

										<div className="text-sm text-gray-600 mb-2">
											Service:{" "}
											<span className="font-medium">
												{item.reservation?.service?.name || "Service supprimé"}
											</span>
											{item.reservation?.service?.category && (
												<span className="text-gray-400">
													{" "}
													• {item.reservation.service.category}
												</span>
											)}
										</div>

										{item.review_text && <p className="text-gray-900 mb-3">{item.review_text}</p>}

										{/* Detailed ratings */}
										{(item.service_quality_rating ||
											item.staff_rating ||
											item.equipment_rating) && (
											<div className="grid grid-cols-3 gap-4 text-sm mb-3">
												{item.service_quality_rating && (
													<div>
														<span className="text-gray-500">Service:</span>
														<div className="flex items-center gap-1">
															{renderStars(item.service_quality_rating)}
														</div>
													</div>
												)}
												{item.staff_rating && (
													<div>
														<span className="text-gray-500">Personnel:</span>
														<div className="flex items-center gap-1">
															{renderStars(item.staff_rating)}
														</div>
													</div>
												)}
												{item.equipment_rating && (
													<div>
														<span className="text-gray-500">Équipement:</span>
														<div className="flex items-center gap-1">
															{renderStars(item.equipment_rating)}
														</div>
													</div>
												)}
											</div>
										)}

										{item.would_recommend !== null && (
											<div className="text-sm mb-3">
												<span className="text-gray-500">Recommanderait: </span>
												<span
													className={item.would_recommend ? "text-green-600" : "text-red-600"}
												>
													{item.would_recommend ? "Oui" : "Non"}
												</span>
											</div>
										)}

										{/* Response */}
										{item.response_text && (
											<div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4 mt-4">
												<div className="flex items-center gap-2 mb-2">
													<Reply className="h-4 w-4 text-emerald-600" />
													<span className="text-sm font-medium text-emerald-800">
														Réponse de{" "}
														{item.responder
															? `${item.responder.first_name} ${item.responder.last_name}`
															: "l'équipe"}
													</span>
													<span className="text-xs text-emerald-600">
														• {formatDate(item.responded_at)}
													</span>
												</div>
												<p className="text-emerald-900">{item.response_text}</p>
											</div>
										)}

										{/* Response Form */}
										{respondingTo === item.id && (
											<div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mt-4">
												<h4 className="text-sm font-medium text-gray-900 mb-3">
													{item.response_text ? "Modifier la réponse" : "Répondre à cet avis"}
												</h4>
												<textarea
													value={responseText}
													onChange={(e) => setResponseText(e.target.value)}
													rows={4}
													className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent mb-3"
													placeholder="Votre réponse..."
												/>
												<div className="flex items-center justify-between">
													<label className="flex items-center gap-2">
														<input
															type="checkbox"
															checked={responsePublic}
															onChange={(e) => setResponsePublic(e.target.checked)}
															className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
														/>
														<span className="text-sm text-gray-700">Réponse publique</span>
													</label>
													<div className="flex gap-2">
														<Button
															variant="outline"
															size="sm"
															onClick={handleCancelResponse}
															icon={X}
															disabled={saving}
														>
															Annuler
														</Button>
														<Button
															size="sm"
															onClick={handleSaveResponse}
															icon={saving ? Loader2 : Save}
															disabled={saving}
														>
															{saving ? "Sauvegarde..." : "Sauvegarder"}
														</Button>
													</div>
												</div>
											</div>
										)}
									</div>

									<div className="flex gap-2 ml-4">
										<Button
											variant="outline"
											size="sm"
											icon={item.is_public ? EyeOff : Eye}
											onClick={() => handleToggleVisibility(item.id, item.is_public)}
											title={item.is_public ? "Rendre privé" : "Rendre public"}
										>
											{item.is_public ? "Masquer" : "Publier"}
										</Button>
										<Button
											variant="outline"
											size="sm"
											icon={Reply}
											onClick={() => handleRespond(item.id, item.response_text || undefined)}
											disabled={saving}
										>
											{item.response_text ? "Modifier" : "Répondre"}
										</Button>
									</div>
								</div>
							</div>
						))
					)}
				</div>
			</div>
		</div>
	);
};

export default AdminCustomerFeedback;
